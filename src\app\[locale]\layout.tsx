import type { Metadata } from "next";

// Ensure the layout is always rendered server-side so language changes apply
export const dynamic = 'force-dynamic';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { locales } from '@/lib/i18n/config';
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { CookieBanner } from "@/components/cookie-banner";
import { Toaster } from "@/components/ui/toaster";
import { PageLoader } from "@/components/layout/page-loader";
import { GoogleTagManager } from "@/components/analytics/google-tag-manager";
import { MetaPixel } from "@/components/analytics/meta-pixel";
import { QueryProvider } from "@/components/layout/query-client-provider";
import "../globals.css";

export const metadata: Metadata = {
  title: "PrimeCaffe - Erstklassiger Kaffeegenuss aus der Schweiz",
  description: "Entdecken Sie unsere Premium-Auswahl an Kaffeekapseln, Bohnen und Zubehör. Stellen Sie Ihre perfekte Coffee Box zusammen oder wählen Sie aus unseren kuratierten Bundles.",
  keywords: "Kaffee, Kaffeekapseln, Kaffeebohnen, Coffee Box, Schweiz, Premium Kaffee",
  authors: [{ name: "PrimeCaffe AG" }],
  creator: "PrimeCaffe AG",
  publisher: "PrimeCaffe AG",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "de_CH",
    url: "https://primecaffe.ch",
    siteName: "PrimeCaffe",
    title: "PrimeCaffe - Erstklassiger Kaffeegenuss aus der Schweiz",
    description: "Entdecken Sie unsere Premium-Auswahl an Kaffeekapseln, Bohnen und Zubehör.",
  },
  twitter: {
    card: "summary_large_image",
    title: "PrimeCaffe - Erstklassiger Kaffeegenuss aus der Schweiz",
    description: "Entdecken Sie unsere Premium-Auswahl an Kaffeekapseln, Bohnen und Zubehör.",
  },
};

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function LocaleLayout({
  children,
  params
}: LocaleLayoutProps) {
  const { locale } = await params;
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as (typeof locales)[number])) {
    notFound();
  }

  // Providing all messages to the client side is the easiest way to get started
  const messages = await getMessages({ locale });

  return (
    <>
      {/* Analytics */}
      {process.env.NEXT_PUBLIC_GTM_ID && (
        <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID} />
      )}
      {process.env.NEXT_PUBLIC_META_PIXEL_ID && (
        <MetaPixel pixelId={process.env.NEXT_PUBLIC_META_PIXEL_ID} />
      )}

      <NextIntlClientProvider messages={messages} locale={locale}>
        <QueryProvider>
          <Header />
          <PageLoader />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
          <CookieBanner />
          <Toaster />
        </QueryProvider>
      </NextIntlClientProvider>
    </>
  );
}

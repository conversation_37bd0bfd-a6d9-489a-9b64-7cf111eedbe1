import { createClient } from '@/lib/supabase/server'
import { ShopContent } from '@/components/shop/shop-content'
import { ShopAnimatedBackground } from '@/components/ui/animated-background'

export const dynamic = 'force-dynamic';

interface ShopPageProps {
  params: Promise<{ locale: string }>;
}

export default async function ShopPage({ params }: ShopPageProps) {
  const { locale } = await params;
  const supabase = await createClient()

  // Get all available products
  const { data: products, error } = await supabase
    .from('products')
    .select('*')
    .eq('is_available', true)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching products:', error)
  }

  return (
    <ShopAnimatedBackground className="min-h-screen">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <ShopContent
            products={products || []}
            locale={locale}
          />
        </div>
      </div>
    </ShopAnimatedBackground>
  )
}
